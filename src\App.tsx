
import './App.css'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import SearchPage from './pages/SearchPage'
import MovieDetailsPage from './pages/MovieDetailsPage'
import ListsPage from './pages/ListsPage'

function App() {
  return (
    // Change BrowserRouter to handle the base URL
    <BrowserRouter basename="/MovieHub">
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="search" element={<SearchPage />} />
          <Route path="movie/:movieId" element={<MovieDetailsPage />} />
          <Route path="lists" element={<ListsPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  )
}

export default App






