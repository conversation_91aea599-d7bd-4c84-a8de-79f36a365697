import { useQuery, useQueries, useQueryClient, UseQueryOptions } from '@tanstack/react-query'
import { 
  getMovieDetails, 
  searchMovies, 
  getNowPlaying, 
  getTopRated, 
  getUpcoming, 
  getTrending, 
  getPopular,
  getSimilarMovies,
  getMoviesByGenre,
  getGenres,
  Movie 
} from '../services/tmdb'
import { useMovieDataStore } from '../store/movieDataStore'

// Query keys for better organization and invalidation
export const movieQueryKeys = {
  all: ['movies'] as const,
  lists: () => [...movieQueryKeys.all, 'list'] as const,
  list: (filters: string) => [...movieQueryKeys.lists(), filters] as const,
  details: () => [...movieQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...movieQueryKeys.details(), id] as const,
  search: (query: string) => [...movieQueryKeys.all, 'search', query] as const,
  genres: () => ['genres'] as const,
  trending: (timeWindow: string) => ['trending', timeWindow] as const,
  similar: (id: string) => ['similar', id] as const,
  byGenre: (genreId: number) => ['movies', 'genre', genreId] as const,
}

// Custom hook for movie details with caching
export function useMovieDetails(movieId: string | undefined) {
  const { setCachedMovie, getCachedMovie, shouldRefetch } = useMovieDataStore()

  return useQuery({
    queryKey: movieQueryKeys.detail(movieId!),
    queryFn: async () => {
      const movie = await getMovieDetails(movieId!)
      setCachedMovie(movie)
      return movie
    },
    enabled: !!movieId,
    staleTime: 1000 * 60 * 30, // 30 minutes for movie details
    gcTime: 1000 * 60 * 60, // 1 hour in cache
    // Use cached data if available and not stale
    initialData: () => {
      if (!movieId) return undefined
      const cached = getCachedMovie(parseInt(movieId))
      return cached && !shouldRefetch(`movie-${movieId}`) ? cached : undefined
    },
  })
}

// Custom hook for search with debouncing
export function useMovieSearch(query: string, enabled: boolean = true) {
  const { addToSearchHistory } = useMovieDataStore()

  return useQuery({
    queryKey: movieQueryKeys.search(query),
    queryFn: async () => {
      const result = await searchMovies(query)
      // Track search in history
      if (query.trim().length > 2) {
        addToSearchHistory(query)
      }
      return result
    },
    enabled: enabled && query.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes for search results
    gcTime: 1000 * 60 * 15, // 15 minutes in cache
  })
}

// Custom hook for trending movies
export function useTrendingMovies(timeWindow: 'day' | 'week' = 'week') {
  const { setLastFetchTime } = useMovieDataStore()

  return useQuery({
    queryKey: movieQueryKeys.trending(timeWindow),
    queryFn: async () => {
      const result = await getTrending(timeWindow)
      setLastFetchTime(`trending-${timeWindow}`, Date.now())
      return result
    },
    staleTime: 1000 * 60 * 30, // 30 minutes
    gcTime: 1000 * 60 * 60, // 1 hour
  })
}

// Custom hook for movie lists (now playing, top rated, etc.)
export function useMovieLists() {
  const { setLastFetchTime } = useMovieDataStore()

  return useQueries({
    queries: [
      {
        queryKey: movieQueryKeys.list('nowPlaying'),
        queryFn: async () => {
          const result = await getNowPlaying()
          setLastFetchTime('nowPlaying', Date.now())
          return result
        },
        staleTime: 1000 * 60 * 15, // 15 minutes
      },
      {
        queryKey: movieQueryKeys.list('topRated'),
        queryFn: async () => {
          const result = await getTopRated()
          setLastFetchTime('topRated', Date.now())
          return result
        },
        staleTime: 1000 * 60 * 30, // 30 minutes (top rated changes less frequently)
      },
      {
        queryKey: movieQueryKeys.list('upcoming'),
        queryFn: async () => {
          const result = await getUpcoming()
          setLastFetchTime('upcoming', Date.now())
          return result
        },
        staleTime: 1000 * 60 * 20, // 20 minutes
      },
      {
        queryKey: movieQueryKeys.list('popular'),
        queryFn: async () => {
          const result = await getPopular()
          setLastFetchTime('popular', Date.now())
          return result
        },
        staleTime: 1000 * 60 * 25, // 25 minutes
      },
    ],
  })
}

// Custom hook for similar movies
export function useSimilarMovies(movieId: string | undefined) {
  return useQuery({
    queryKey: movieQueryKeys.similar(movieId!),
    queryFn: () => getSimilarMovies(movieId!),
    enabled: !!movieId,
    staleTime: 1000 * 60 * 20, // 20 minutes
    gcTime: 1000 * 60 * 40, // 40 minutes
  })
}

// Custom hook for movies by genre
export function useMoviesByGenre(genreId: number | null) {
  const { updateGenrePreference } = useMovieDataStore()

  return useQuery({
    queryKey: movieQueryKeys.byGenre(genreId!),
    queryFn: async () => {
      const result = await getMoviesByGenre(genreId!)
      // Track genre interaction
      updateGenrePreference(genreId!)
      return result
    },
    enabled: !!genreId,
    staleTime: 1000 * 60 * 15, // 15 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  })
}

// Custom hook for genres
export function useGenres() {
  return useQuery({
    queryKey: movieQueryKeys.genres(),
    queryFn: getGenres,
    staleTime: 1000 * 60 * 60 * 24, // 24 hours (genres rarely change)
    gcTime: 1000 * 60 * 60 * 24 * 7, // 1 week
  })
}

// Custom hook for prefetching movie details
export function usePrefetchMovieDetails() {
  const queryClient = useQueryClient()

  return (movieId: number) => {
    queryClient.prefetchQuery({
      queryKey: movieQueryKeys.detail(movieId.toString()),
      queryFn: () => getMovieDetails(movieId.toString()),
      staleTime: 1000 * 60 * 30,
    })
  }
}

// Utility function to invalidate movie-related queries
export function useInvalidateMovieQueries() {
  const queryClient = useQueryClient()

  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: movieQueryKeys.all }),
    invalidateLists: () => queryClient.invalidateQueries({ queryKey: movieQueryKeys.lists() }),
    invalidateDetails: () => queryClient.invalidateQueries({ queryKey: movieQueryKeys.details() }),
    invalidateSearch: () => queryClient.invalidateQueries({ queryKey: ['movies', 'search'] }),
  }
}
