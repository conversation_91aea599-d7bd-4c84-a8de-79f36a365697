import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import './index.css'
import App from './App'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache for 15 minutes by default
      staleTime: 1000 * 60 * 15,
      // Keep in memory for 1 hour
      gcTime: 1000 * 60 * 60,
      // Don't refetch on window focus (better UX)
      refetchOnWindowFocus: false,
      // Refetch when reconnecting to internet
      refetchOnReconnect: true,
      // Refetch on mount only if data is stale
      refetchOnMount: 'stale',
      // Smart retry logic
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx client errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }
        // Don't retry more than 3 times
        if (failureCount >= 3) {
          return false
        }
        // Retry on network errors and 5xx server errors
        return true
      },
      // Exponential backoff with jitter
      retryDelay: (attemptIndex) => {
        const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000)
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.1 * baseDelay
        return baseDelay + jitter
      },
      // Network mode for better offline handling
      networkMode: 'online',
    },
    mutations: {
      // Retry mutations once on failure
      retry: 1,
      retryDelay: 1000,
      // Network mode for mutations
      networkMode: 'online',
    },
  },
  // Global error handler
  queryCache: {
    onError: (error: any) => {
      console.error('Query error:', error)
      // You could add toast notifications here
    },
  },
  mutationCache: {
    onError: (error: any) => {
      console.error('Mutation error:', error)
      // You could add toast notifications here
    },
  },
})

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </StrictMode>,
)


